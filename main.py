import tkinter as tk
from tkinter import simpledialog, messagebox, filedialog
from PIL import ImageGrab
from pynput.mouse import Controller as MouseController

class ColorPickerGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("取色")
        
        self.mouse = MouseController()

        self.label = tk.Label(master, text="鼠标移动到指定位置然后按 Enter.")
        self.label.pack()

        self.pick_color_btn = tk.But<PERSON>(master, text="取色（按Enter）", command=self.pick_color_and_bind_key)
        self.pick_color_btn.pack()

        # 给按钮设置焦点以便可以通过键盘事件触发
        self.pick_color_btn.focus_set()

        # 绑定回车键到pick_color函数
        self.master.bind('<Return>', lambda event: self.pick_color_and_bind_key())

        self.close_btn = tk.Button(master, text="完成并生成", command=self.finish_and_generate_script)
        self.close_btn.pack()

        self.color_bindings = []

    def pick_color_and_bind_key(self):
        # Capture the color at the current mouse position and its surrounding pixels
        x, y = self.mouse.position
        colors = self.get_surrounding_colors(x, y)
        # Ask for the key to bind with these colors
        key = simpledialog.askstring("按键绑定", "输入想要绑定的按键:", parent=self.master)
        if key:
            self.color_bindings.append({"colors": colors, "key": key})
            messagebox.showinfo("Color Picked and Key Bound", f"Colors at ({x}, {y}) and surrounding bound to key: {key}")

    def get_surrounding_colors(self, x, y):
        offsets = [(0, 0), (-3, 0), (3, 0), (0, -3), (0, 3)]
        colors = []
        
        # 捕获屏幕截图
        screenshot = ImageGrab.grab()
        
        for dx, dy in offsets:
            px = x + dx
            py = y + dy
            try:
                color = screenshot.getpixel((px, py))
                # 如果返回的是RGB元组，转换为十六进制
                if isinstance(color, tuple):
                    r, g, b = color[:3]  # 取前三个值 (RGB)
                    color_hex = f"{r:02X}{g:02X}{b:02X}"[-4:]  # 只取后4位
                else:
                    color_hex = f"{color:06X}"[-4:]  # 如果是整数，转换为十六进制后取后4位
                colors.append({"x": px, "y": py, "color": color_hex})
            except IndexError:
                # 如果坐标超出屏幕范围，跳过这个点位
                continue
            
        return colors

    def finish_and_generate_script(self):
        filename = filedialog.asksaveasfilename(defaultextension=".ahk", filetypes=[("Scripts", "*.test")], parent=self.master)
        if filename:
            with open(filename, 'w') as file:
                file.write(";\n#NoEnv\n#Warn\nSendMode Input\nSetWorkingDir %A_ScriptDir%\n\nXButton1::\nSetTimer,CheckColors, 0\nKeyWait, XButton1\nSetTimer,CheckColors, Off\nReturn\n\nCheckColors:\n")
                for binding in self.color_bindings:
                    conditions = " and ".join([f'ColorMatch(GetColor({color["x"]},{color["y"]}),"{color["color"]}")' for color in binding["colors"]])
                    file.write(f"    if {conditions}:\n")
                    file.write(f"            press_key('{{binding['key']}}')\n    ")  # 使用AHK的Send命令而不是pyautogui
                file.write("Return\n\nGetColor(x,y)\n{\n    CoordMode, Pixel, Screen\n    PixelGetColor, color, x, y, RGB\n    StringRight, color, color, 4\n    return color\n}\n")
            messagebox.showinfo("Success", "AHK script generated successfully!")

if __name__ == "__main__":
    root = tk.Tk()
    my_gui = ColorPickerGUI(root)
    root.mainloop()
